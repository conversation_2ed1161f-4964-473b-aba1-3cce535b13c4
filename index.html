<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NeuroScan - Analyse de tumeurs cérébrales</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .upload-area {
            border: 2px dashed #3b82f6;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #2563eb;
            background-color: #f8fafc;
        }
        .progress-bar {
            height: 6px;
            transition: width 0.3s ease;
        }
        .highlight-area {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 0.7; }
            50% { opacity: 0.9; }
            100% { opacity: 0.7; }
        }
        #resultImage {
            max-height: 400px;
            object-fit: contain;
        }
        .diagnosis-badge {
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-weight: 600;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <i class="fas fa-brain text-blue-600 text-2xl"></i>
                <h1 class="text-2xl font-bold text-gray-800">NeuroScan</h1>
            </div>
            <nav class="hidden md:flex space-x-6">
                <a href="#" class="text-blue-600 font-medium">Accueil</a>
                <a href="#" class="text-gray-600 hover:text-blue-600">À propos</a>
                <a href="#" class="text-gray-600 hover:text-blue-600">Documentation</a>
                <a href="#" class="text-gray-600 hover:text-blue-600">Contact</a>
            </nav>
            <button class="md:hidden text-gray-600">
                <i class="fas fa-bars text-xl"></i>
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Hero Section -->
        <section class="mb-12 text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Analyse avancée de tumeurs cérébrales</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                Notre système d'IA assiste les professionnels de santé dans le diagnostic des tumeurs cérébrales à partir d'images IRM.
            </p>
        </section>

        <!-- Upload & Analysis Section -->
        <section class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            <!-- Upload Card -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-upload mr-2 text-blue-500"></i> Charger une image IRM
                    </h3>
                    
                    <div id="uploadContainer" class="upload-area rounded-lg p-8 text-center cursor-pointer mb-4">
                        <i class="fas fa-cloud-upload-alt text-4xl text-blue-400 mb-3"></i>
                        <p class="text-gray-600 mb-2">Glissez-déposez votre fichier IRM ici ou</p>
                        <button id="selectFileBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition">
                            Sélectionner un fichier
                        </button>
                        <p class="text-xs text-gray-500 mt-3">Formats supportés: .dcm, .nii, .jpg, .png</p>
                        <input type="file" id="fileInput" class="hidden" accept=".dcm,.nii,.jpg,.png">
                    </div>
                    
                    <div id="fileInfo" class="hidden bg-gray-50 p-4 rounded-lg mb-4">
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center">
                                <i class="fas fa-file-image text-blue-500 mr-2"></i>
                                <span id="fileName" class="font-medium text-gray-700"></span>
                            </div>
                            <button id="removeFileBtn" class="text-red-500 hover:text-red-700">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-1.5">
                            <div id="uploadProgress" class="progress-bar bg-blue-600 h-1.5 rounded-full" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <button id="analyzeBtn" disabled class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center">
                        <i class="fas fa-search mr-2"></i> Lancer l'analyse
                    </button>
                </div>
            </div>
            
            <!-- Analysis Progress Card -->
            <div id="progressCard" class="bg-white rounded-xl shadow-md overflow-hidden hidden">
                <div class="p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-spinner fa-spin mr-2 text-blue-500"></i> Analyse en cours
                    </h3>
                    
                    <div class="mb-6">
                        <div class="flex justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700">Progression de l'analyse</span>
                            <span id="progressPercent" class="text-sm font-medium text-gray-700">0%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div id="analysisProgress" class="bg-blue-600 h-2.5 rounded-full" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-check text-blue-600 text-xs"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-700">Pré-traitement de l'image</p>
                                <p class="text-xs text-gray-500">Normalisation et amélioration du contraste</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i id="segmentationIcon" class="fas fa-spinner fa-spin text-blue-600 text-xs"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-700">Segmentation des tissus</p>
                                <p class="text-xs text-gray-500">Identification des régions d'intérêt</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                                <i id="classificationIcon" class="fas fa-circle text-gray-300 text-xs"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">Classification de la tumeur</p>
                                <p class="text-xs text-gray-400">Analyse des caractéristiques morphologiques</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Results Card -->
            <div id="resultsCard" class="bg-white rounded-xl shadow-md overflow-hidden hidden lg:col-span-2">
                <div class="p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <i class="fas fa-clipboard-check text-blue-500 mr-2"></i> Résultats de l'analyse
                    </h3>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- Image with Annotations -->
                        <div class="lg:col-span-2 bg-gray-50 rounded-lg p-4">
                            <div class="relative">
                                <img id="resultImage" src="" alt="Résultat d'analyse" class="w-full rounded-lg border border-gray-200">
                                <div id="highlightAreas" class="absolute inset-0"></div>
                            </div>
                            <div class="mt-3 flex justify-center space-x-2">
                                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                                    <i class="fas fa-layer-group mr-1"></i> Couches
                                </button>
                                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                                    <i class="fas fa-ruler-combined mr-1"></i> Mesures
                                </button>
                                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                                    <i class="fas fa-download mr-1"></i> Exporter
                                </button>
                            </div>
                        </div>
                        
                        <!-- Diagnosis Information -->
                        <div class="space-y-6">
                            <!-- Diagnosis Summary -->
                            <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                                <h4 class="font-medium text-gray-700 mb-3">Résumé du diagnostic</h4>
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-600">Probabilité de tumeur:</span>
                                    <span id="tumorProbability" class="font-medium">89%</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">Diagnostic principal:</span>
                                    <span id="mainDiagnosis" class="diagnosis-badge bg-red-100 text-red-800">Suspect</span>
                                </div>
                            </div>
                            
                            <!-- Tumor Characteristics -->
                            <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                                <h4 class="font-medium text-gray-700 mb-3">Caractéristiques de la tumeur</h4>
                                <div class="space-y-3">
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span class="text-gray-600">Méningiome</span>
                                            <span id="meningiomaProb" class="font-medium text-gray-800">12%</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-1.5">
                                            <div class="bg-blue-400 h-1.5 rounded-full" style="width: 12%"></div>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span class="text-gray-600">Gliome</span>
                                            <span id="gliomaProb" class="font-medium text-gray-800">34%</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-1.5">
                                            <div class="bg-blue-500 h-1.5 rounded-full" style="width: 34%"></div>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span class="text-gray-600">Métastase</span>
                                            <span id="metastasisProb" class="font-medium text-gray-800">43%</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-1.5">
                                            <div class="bg-blue-600 h-1.5 rounded-full" style="width: 43%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Recommendations -->
                            <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                                <h4 class="font-medium text-gray-700 mb-3">Recommandations</h4>
                                <ul class="space-y-2 text-sm text-gray-600">
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2 flex-shrink-0"></i>
                                        <span>Biopsie recommandée pour confirmation histologique</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2 flex-shrink-0"></i>
                                        <span>IRM de suivi dans 3 mois pour évaluation de la croissance</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mt-1 mr-2 flex-shrink-0"></i>
                                        <span>Consultation avec un neuro-oncologue spécialisé</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex flex-wrap gap-3">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition flex items-center">
                            <i class="fas fa-file-pdf mr-2"></i> Générer un rapport
                        </button>
                        <button class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 border border-gray-300 rounded-lg transition flex items-center">
                            <i class="fas fa-share-alt mr-2"></i> Partager avec un collègue
                        </button>
                        <button class="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 border border-gray-300 rounded-lg transition flex items-center">
                            <i class="fas fa-history mr-2"></i> Comparer avec des précédents
                        </button>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Information Section -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">Comment fonctionne notre analyse ?</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-robot text-blue-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Intelligence Artificielle</h3>
                    <p class="text-gray-600">Notre modèle deep learning a été entraîné sur plus de 10 000 cas validés par des experts en neuroradiologie.</p>
                </div>
                
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-chart-line text-green-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Métriques quantitatives</h3>
                    <p class="text-gray-600">Analyse précise de la taille, de la forme, de la texture et des caractéristiques de rehaussement de la lésion.</p>
                </div>
                
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-user-md text-purple-600"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Support décisionnel</h3>
                    <p class="text-gray-600">Fournit des recommandations basées sur les dernières guidelines cliniques pour chaque cas analysé.</p>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-brain mr-2"></i> NeuroScan
                    </h3>
                    <p class="text-gray-400 text-sm">Solution d'aide au diagnostic des tumeurs cérébrales pour les professionnels de santé.</p>
                </div>
                <div>
                    <h4 class="text-md font-semibold mb-4">Navigation</h4>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="#" class="hover:text-white">Accueil</a></li>
                        <li><a href="#" class="hover:text-white">À propos</a></li>
                        <li><a href="#" class="hover:text-white">Documentation</a></li>
                        <li><a href="#" class="hover:text-white">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-md font-semibold mb-4">Légal</h4>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="#" class="hover:text-white">Confidentialité</a></li>
                        <li><a href="#" class="hover:text-white">Conditions d'utilisation</a></li>
                        <li><a href="#" class="hover:text-white">Certifications</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-md font-semibold mb-4">Contact</h4>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li class="flex items-center"><i class="fas fa-envelope mr-2"></i> <EMAIL></li>
                        <li class="flex items-center"><i class="fas fa-phone mr-2"></i> +33 1 23 45 67 89</li>
                        <li class="flex items-center"><i class="fas fa-map-marker-alt mr-2"></i> Paris, France</li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-6 text-sm text-gray-400 text-center">
                <p>© 2023 NeuroScan. Tous droits réservés. CE marqué. Dispositif médical de classe IIa.</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // DOM Elements
            const uploadContainer = document.getElementById('uploadContainer');
            const selectFileBtn = document.getElementById('selectFileBtn');
            const fileInput = document.getElementById('fileInput');
            const fileInfo = document.getElementById('fileInfo');
            const fileName = document.getElementById('fileName');
            const removeFileBtn = document.getElementById('removeFileBtn');
            const uploadProgress = document.getElementById('uploadProgress');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const progressCard = document.getElementById('progressCard');
            const resultsCard = document.getElementById('resultsCard');
            const progressPercent = document.getElementById('progressPercent');
            const analysisProgress = document.getElementById('analysisProgress');
            const segmentationIcon = document.getElementById('segmentationIcon');
            const classificationIcon = document.getElementById('classificationIcon');
            const resultImage = document.getElementById('resultImage');
            const highlightAreas = document.getElementById('highlightAreas');
            const tumorProbability = document.getElementById('tumorProbability');
            const mainDiagnosis = document.getElementById('mainDiagnosis');
            const meningiomaProb = document.getElementById('meningiomaProb');
            const gliomaProb = document.getElementById('gliomaProb');
            const metastasisProb = document.getElementById('metastasisProb');
            
            // Drag and drop functionality
            uploadContainer.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadContainer.classList.add('border-blue-500', 'bg-blue-50');
            });
            
            uploadContainer.addEventListener('dragleave', () => {
                uploadContainer.classList.remove('border-blue-500', 'bg-blue-50');
            });
            
            uploadContainer.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadContainer.classList.remove('border-blue-500', 'bg-blue-50');
                if (e.dataTransfer.files.length) {
                    handleFileSelection(e.dataTransfer.files[0]);
                }
            });
            
            // File selection
            selectFileBtn.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', () => {
                if (fileInput.files.length) {
                    handleFileSelection(fileInput.files[0]);
                }
            });
            
            // Remove file
            removeFileBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                resetFileUpload();
            });
            
            // Analyze button
            analyzeBtn.addEventListener('click', startAnalysis);
            
            // Handle file selection
            function handleFileSelection(file) {
                // Validate file type
                const validTypes = ['image/dicom', 'image/nifti', 'image/jpeg', 'image/png', 'application/dicom'];
                const fileExt = file.name.split('.').pop().toLowerCase();
                
                if (!validTypes.includes(file.type) && !['dcm', 'nii', 'jpg', 'jpeg', 'png'].includes(fileExt)) {
                    alert('Format de fichier non supporté. Veuillez uploader une image IRM (DICOM, NIfTI, JPEG, PNG).');
                    return;
                }
                
                // Display file info
                fileName.textContent = file.name;
                fileInfo.classList.remove('hidden');
                analyzeBtn.disabled = false;
                
                // Simulate upload progress
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 10;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                    }
                    uploadProgress.style.width = `${progress}%`;
                }, 200);
            }
            
            // Reset file upload
            function resetFileUpload() {
                fileInput.value = '';
                fileInfo.classList.add('hidden');
                analyzeBtn.disabled = true;
                uploadProgress.style.width = '0%';
            }
            
            // Start analysis
            function startAnalysis() {
                // Show progress card
                progressCard.classList.remove('hidden');
                
                // Simulate analysis progress
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 5;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                        
                        // Update icons when steps complete
                        segmentationIcon.classList.remove('fa-spinner', 'fa-spin');
                        segmentationIcon.classList.add('fa-check');
                        
                        setTimeout(() => {
                            classificationIcon.classList.remove('fa-circle', 'text-gray-300');
                            classificationIcon.classList.add('fa-spinner', 'fa-spin', 'text-blue-600');
                            
                            setTimeout(() => {
                                classificationIcon.classList.remove('fa-spinner', 'fa-spin');
                                classificationIcon.classList.add('fa-check');
                                
                                // Show results after a short delay
                                setTimeout(showResults, 500);
                            }, 1500);
                        }, 1000);
                    }
                    progressPercent.textContent = `${Math.min(progress, 100).toFixed(0)}%`;
                    analysisProgress.style.width = `${Math.min(progress, 100)}%`;
                }, 200);
            }
            
            // Show results
            function showResults() {
                progressCard.classList.add('hidden');
                resultsCard.classList.remove('hidden');
                
                // For demo purposes, we'll use a sample image and results
                resultImage.src = 'https://radiopaedia.org/images/2091727/9e1d7d1f3f4a8f1f4a7e5e5d5b5c5a5b.jpeg';
                
                // Add sample highlight areas (would be generated from real analysis)
                const highlightHTML = `
                    <div class="highlight-area absolute w-20 h-20 rounded-full border-2 border-red-500 bg-red-400 bg-opacity-20" style="top: 30%; left: 45%;"></div>
                    <div class="highlight-area absolute w-16 h-16 rounded-full border-2 border-yellow-500 bg-yellow-400 bg-opacity-20" style="top: 40%; left: 55%;"></div>
                `;
                highlightAreas.innerHTML = highlightHTML;
                
                // Set sample results
                tumorProbability.textContent = '89%';
                mainDiagnosis.textContent = 'Suspect';
                mainDiagnosis.className = 'diagnosis-badge bg-red-100 text-red-800';
                
                meningiomaProb.textContent = '12%';
                gliomaProb.textContent = '34%';
                metastasisProb.textContent = '43%';
                
                // Scroll to results
                resultsCard.scrollIntoView({ behavior: 'smooth' });
            }
        });
    </script>
</body>
</html>